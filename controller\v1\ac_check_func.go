package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

/************************************ 检查函数 ************************************/
/*
检查函数发生错误时将设置错误信息，并返回false；在检查函数返回false时，应当立即返回
example:
if !check_function(c, ...) {
    return
}
/*********************************************************************************/

// 跳过样机登录检测的机型
var skipExampleCheckModel = map[string]struct{}{
	ax3Model:    {},
	ax3ModelTpl: {}, // TODO 删除
	ax5Model:    {},
}

// 爱学空间机型
var axkjModel = map[string]struct{}{
	ax3Model:    {},
	ax3ModelTpl: {}, // TODO 删除
	ax5Model:    {},
}

// 爱学空间机型定义。TODO 确认爱学空间样机机型
const (
	ax3Model    = "Readboy_Z3"
	ax3ModelTpl = "Readboy_A3"
	ax5Model    = "Readboy_Z5"
)

// CheckExampleMachine 样机检测
// 非样机或样机登录成功返回true，否则返回false
func CheckExampleMachine(c *gin.Context, username string) bool {
	if username == "" {
		return true
	}
	agent := c.Request.UserAgent()
	if agent == "" {
		return true
	}
	if strings.Index(agent, "-i") != 0 {
		return true
	}
	ids := strings.Split(agent, "-")
	itemNo := 1
	serial := ""
	dmodel := ""
	for _, sec := range ids {
		if len(sec) == 0 {
			continue
		}
		func() {
			defer func() { recover() }()
			switch sec[0] {
			case 'i':
				dmodel = sec[1:]
			case 'r', 'v', 'd', 't', 'm':
				itemNo++
			case 's':
				itemNo++
				serial = sec[1:]
			}
		}()
	}
	if itemNo <= 3 || serial == "" {
		return true
	}
	if _, ok := skipExampleCheckModel[dmodel]; ok {
		return true
	}
	msg, errlogin := utils.ExampleMachineLogin(serial, username)
	if errlogin != nil {
		global.LOG.Debug("样机登录调用错误", zap.Error(errlogin))
	}
	if msg != "" {
		response.FailWithErrcode(response.E_ACCESS, msg, c)
		return false
	}
	return true
}

// CheckLogin 检查是否登录
func CheckLogin(c *gin.Context) (member *model.AcMember, ok bool) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	if reason == 0 || m.Uid == 0 {
		response.FailWithErrcode(response.E_NOT_LOGIN, "", c)
		return
	}
	if !CheckMemberForbidden(c, m) {
		return nil, false
	}
	return m, true
}

// CheckMemberForbidden 检查账号禁用标识
func CheckMemberForbidden(c *gin.Context, m *model.AcMember) bool {
	if m == nil {
		response.FailWithErrcode(response.E_UNKNOWN, "", c)
		return false
	}
	if m.Forbidden == 1 {
		response.FailWithErrcode(response.E_FORBIDDEN, "", c)
		return false
	}
	if m.Forbidden == 4 {
		response.FailWithErrcode(response.E_UNACTIVE, "", c)
		return false
	}
	return true
}

func CheckVerify(c *gin.Context, username string) bool {
	serial := GetRequestParam(c, "serial")
	verify := GetRequestParam(c, "verify")
	if serial == "" {
		response.FailWithErrcode(response.E_INVALID_SERIAL, "", c)
		return false
	}
	if verify == "" {
		response.FailWithErrcode(response.E_INVALID_VERIFY, "", c)
		return false
	}
	v, err := service.GetVerifyById(serial)
	if err != nil {
		response.FailWithErrcode(response.E_VERIFY, "", c)
		return false
	}
	// 已使用
	if v.Status != 1 {
		response.FailWithErrcode(response.E_INVALID_SERIAL, "", c)
		return false
	}
	// 账号不匹配
	if v.Username != username {
		response.FailWithErrcode(response.E_ACCOUNT_MISMATCH, "", c)
		return false
	}
	// 验证码错误
	if v.Verify != verify {
		response.FailWithErrcode(response.E_VERIFY, "", c)
		return false
	}
	service.SetVerifyStatus(v.ID, 2)
	// 失效
	if v.Expire <= time.Now().Unix() {
		response.FailWithErrcode(response.E_SERIAL_EXPIRED, "", c)
		return false
	}
	return true
}

// CheckSMSVerify 验证短信验证码，成功时返回国际区号
func CheckSMSVerify(c *gin.Context, mobile string) (country_code string, ok bool) {
	serial := GetRequestParam(c, "serial")
	verify := GetRequestParam(c, "verify")
	if serial == "" {
		response.FailWithErrcode(response.E_INVALID_SERIAL, "", c)
		return
	}
	if verify == "" {
		response.FailWithErrcode(response.E_INVALID_VERIFY, "", c)
		return
	}
	v, err := service.GetVerifyById(serial)
	if err != nil {
		response.FailWithErrcode(response.E_VERIFY, "", c)
		return
	}
	// 已使用
	if v.Status != 1 {
		response.FailWithErrcode(response.E_INVALID_SERIAL, "", c)
		return
	}
	// 账号不匹配
	if v.Username != mobile {
		response.FailWithErrcode(response.E_ACCOUNT_MISMATCH, "", c)
		return
	}
	// 验证码错误
	if v.Verify != verify {
		response.FailWithErrcode(response.E_VERIFY, "", c)
		return
	}
	service.SetVerifyStatus(v.ID, 2)
	// 失效
	if v.Expire <= time.Now().Unix() {
		response.FailWithErrcode(response.E_SERIAL_EXPIRED, "", c)
		return
	}
	country_code = v.CountryCode
	ok = true
	return
}

func GetAndCheckMobile(c *gin.Context) (mobile string, exists bool) {
	mobile = GetRequestParam(c, "mobile")
	if mobile == "" {
		response.FailWithErrcode(response.E_REQUIRE_MOBILE, "", c)
		return
	}
	if encrypt, ok := GetAndCheckEncrypt(c); !ok {
		return
	} else if encrypt == 1 {
		mobile = DecryptParam(c, mobile)
	}
	if !utils.TestPhone(mobile) {
		response.FailWithErrcode(response.E_MOBILE_FMT, "", c)
		return
	}
	exists = true
	return
}

func GetAndCheckUnionId(c *gin.Context) (unionid string, exists bool) {
	unionid = GetRequestParam(c, "unionid")
	if unionid == "" {
		response.FailWithErrcode(response.E_REQUIRE_USERNAME, "未填写unionid", c)
		return
	} else if !utils.TestWxUnionid(unionid) {
		response.FailWithErrcode(response.E_USERNAME_FMT, "unionid格式错误", c)
		return
	}
	exists = true
	return
}

func GetAndCheckCtdidPid(c *gin.Context) (ctdidPid string, exists bool) {
	ctdidPid = GetRequestParam(c, "ctdid_pid")
	if ctdidPid == "" {
		response.FailWithErrcode(response.E_REQUIRE_USERNAME, "未填写ctdid_pid", c)
		return
	} else if !utils.TestCtdidPid(ctdidPid) {
		response.FailWithErrcode(response.E_USERNAME_FMT, "ctdid_pid格式错误", c)
		return
	}
	exists = true
	return
}

func GetAndCheckEmail(c *gin.Context) (email string, exists bool) {
	email = GetRequestParam(c, "email")
	if email == "" {
		response.FailWithErrcode(response.E_REQUIRE_EMAIL, "", c)
		return
	} else if !utils.TestEmail(email) {
		response.FailWithErrcode(response.E_EMAIL_FMT, "", c)
		return
	}
	exists = true
	return
}

func GetAndCheckPassword(c *gin.Context) (password string, exists bool) {
	password = GetRequestParam(c, "password")
	if password == "" {
		response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
		return
	} else if !utils.TestPassword(password) {
		response.FailWithErrcode(response.E_PASSWORD_FMT, "", c)
		return
	}
	exists = true
	return
}

func CheckNonce(c *gin.Context, group string, ttl int) (nonce string, ok bool) {
	nonce = GetRequestParam(c, "nonce")
	key := fmt.Sprintf("account:nonce:%s", group)
	now := time.Now().Unix()
	minScore := now - int64(ttl) - 1
	global.Redis.ZRemRangeByScore(key, "0", strconv.FormatInt(minScore, 10))
	if exists, _ := global.Redis.SIsMember(key, nonce).Result(); exists {
		if score, _ := global.Redis.ZScore(key, group).Result(); score <= float64(minScore) {
			response.FailWithErrcode(response.E_ACCESS, "", c)
			return nonce, false
		}
	} else {
		global.Redis.ZAdd(key, redis.Z{
			Score:  float64(now),
			Member: nonce,
		})
		global.Redis.Expire(key, time.Minute*10+time.Duration(rand.Intn(30))*time.Second)
	}
	return nonce, true
}

// 检查设备是否允许登录该账号
func CheckDeviceLoginMember(c *gin.Context, m *model.AcMember) bool {
	serial := GetDeviceSerial(c)
	if serial == "" {
		return true
	}
	dm, err := service.GetDeviceMember(serial, m.Uid)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		response.FailWithError(response.New(response.E_DATABASE, "", err), c)
		return false
	}
	model := GetDeviceModel(c)
	var exMachine bool
	_, isAxkjModel := axkjModel[model]

	if isAxkjModel { // Z系列不再限制登录
		return true
	}
	// TODO: 后续可能还有其他机型
	if dm.Uid == 0 {
		if isAxkjModel && !exMachine {
			/* A3 非样机必须绑定登录账号 */
			response.FailWithErrcode(response.E_UNAMEPWD, "", c)
			return false
		}
	}

	// A3 是样机的最多只能登录5个账号
	if isAxkjModel && exMachine && dm.Uid > 0 && dm.Uid != m.Uid {
		if err1 := service.BindDeviceMember2(&dm, 5); err1 != nil {
			response.FailWithError(err1, c)
			return false
		} else {
			return true
		}
	}
	if dm.Uid > 0 && dm.Uid != m.Uid {
		response.FailWithErrcode(response.E_UNAMEPWD, "", c)
		return false
	}
	return true
}

func GetAndCheckEncrypt(c *gin.Context) (data int, ok bool) {
	if encrypt := GetRequestParam(c, "encrypt"); encrypt == "1" {
		c.Set("encrypt", 1)
		data = 1
		key := c.GetString("appsec")
		if key == "" {
			if reason, desp := ParseSignature(c); reason < 0 {
				response.FailWithDetail(response.E_INVALID_SN, "", gin.H{"reason": reason, "desp": desp}, c)
				return
			}

			appid := c.GetString("appid")
			global.AppsRWMutex.RLock()
			key = global.Apps[strings.ToLower(appid)]
			global.AppsRWMutex.RUnlock()
			c.Set("appsec", key)
		}
		ok = true
		return 1, true
	}
	ok = true
	return
}

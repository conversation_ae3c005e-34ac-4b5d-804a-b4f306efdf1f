package model

import (
	"strconv"

	"github.com/go-playground/validator/v10"
)

type UserProfile struct {
	Uid           int    `form:"-" gorm:"primarykey" json:"-"`
	Username      string `form:"-" json:"-"`
	Realname      string `binding:"omitempty,min=2,max=60" form:"realname" json:"realname"`           // 实际为昵称
	Gender        uint   `binding:"omitempty,oneof=0 1" form:"gender" json:"gender" gorm:"default:0"` // 性别，0：男，1：女
	BirthY        int    `binding:"omitempty,min=1000" form:"birth_y" json:"birth_y" gorm:"default:2008"`
	BirthM        int    `binding:"omitempty,min=1,max=12" form:"birth_m" json:"birth_m" gorm:"default:1"`
	BirthD        int    `binding:"omitempty,min=1,max=31" form:"birth_d" json:"birth_d" gorm:"default:1"`
	Constellation string `binding:"omitempty,min=2,max=10" form:"constellation" json:"constellation"`         // 星座
	Zodiac        string `binding:"omitempty" form:"zodiac" json:"zodiac"`                                    // 生肖
	Height        uint   `binding:"omitempty,min=1" form:"height" json:"height" gorm:"default:120"`           // 身高(cm)
	Weight        uint   `binding:"omitempty,min=1" form:"weight" json:"weight" gorm:"default:30"`            // 体重(kg)
	Bloodtype     int    `binding:"omitempty,min=1,max=5" form:"bloodtype" json:"bloodtype" gorm:"default:5"` // 血型，1-A, 2-B, 3-AB, 4-O, 5-其他
	ProvinceId    uint   `binding:"omitempty,min=11,max=99" form:"province_id" json:"province_id" gorm:"default:11"`
	CityId        uint   `binding:"omitempty,min=1101,max=9999" form:"city_id" json:"city_id" gorm:"default:1101"`
	DistrictId    uint   `binding:"omitempty,min=110101,max=999999999" form:"district_id" json:"district_id" gorm:"default:110101"`
	Address       string `binding:"omitempty,min=2,max=255" form:"address" json:"address"`
	School        string `binding:"omitempty,min=2,max=100" form:"school"  json:"school"`
	Grade         int    `binding:"omitempty" form:"grade" json:"grade"`             // 256+n表示学前，512+n表示小学到初中，768+n表示高中
	Sign          string `binding:"omitempty,max=127" form:"sign" json:"sign"`       // 个性签名
	Explain       string `binding:"omitempty,max=255" form:"explain" json:"explain"` // 个人说明
	QQ            string `binding:"omitempty,min=5,max=10" form:"qq" json:"qq"`
	Weixin        string `binding:"omitempty,min=2,max=64" form:"weixin" json:"weixin"`
	Weibo         string `binding:"omitempty,min=2,max=64" form:"weibo" json:"weibo"`
}

func (UserProfile) TableName() string {
	return "user_profile"
}

func (UserProfile) GetError(err validator.ValidationErrors) string {
	if len(err) == 0 {
		return "参数错误"
	}
	val := err[0]
	switch val.Field() {
	case "Realname":
		return "昵称长度不合法"
	case "Gender":
		return "性别须为0或1"
	case "BirthY":
		return "出生年须为4位数字"
	case "BirthM":
		return "出生月须为1-12"
	case "BirthD":
		return "出生日须为1-2位数字"
	case "Constellation":
		return "星座须为2-10个字符"
	case "Height":
		return "升高须为1-3位数字"
	case "Weight":
		return "体重须为1-3位数字"
	case "Bloodtype":
		return "血型须为1,2,3,4,5"
	case "ProvinceId":
		return "省份id须为2位数字"
	case "CityId":
		return "城市id须为4位数字"
	case "DistrictId":
		return "市区id须为6-9位数字"
	case "Address":
		return "地址须为2-255个字符"
	case "School":
		return "学校须为2-100个字符"
	case "Grade":
		return "年级须为1-3位数字"
	case "Sign":
		return "个性签名应小于128个字"
	case "Explain":
		return "个人说明应小于255个字"
	case "QQ":
		return "qq须为5-10位数字"
	case "Weixin":
		return "微信须为2-64个字符"
	case "Weibo":
		return "微博须为2-64个字符"
	}
	return "参数错误"
}

func (p UserProfile) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"uid":           strconv.Itoa(p.Uid),
		"username":      p.Username,
		"realname":      p.Realname,
		"gender":        strconv.Itoa(int(p.Gender)),
		"birth_y":       strconv.Itoa(p.BirthY),
		"birth_m":       strconv.Itoa(p.BirthM),
		"birth_d":       strconv.Itoa(p.BirthD),
		"constellation": p.Constellation,
		"zodiac":        p.Zodiac,
		"height":        strconv.Itoa(int(p.Height)),
		"weight":        strconv.Itoa(int(p.Weight)),
		"bloodtype":     strconv.Itoa(p.Bloodtype),
		"province_id":   strconv.Itoa(int(p.ProvinceId)),
		"city_id":       strconv.Itoa(int(p.CityId)),
		"district_id":   strconv.Itoa(int(p.DistrictId)),
		"address":       p.Address,
		"school":        p.School,
		"grade":         strconv.Itoa(p.Grade),
		"sign":          p.Sign,
		"explain":       p.Explain,
		"qq":            p.QQ,
		"weixin":        p.Weixin,
		"weibo":         p.Weibo,
	}
}

package service

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/utils"
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis"
	"gorm.io/gorm"
)

// GetMemberCount 获取用户总数
func GetMemberCount() (count int64, err error) {
	db := global.DB.Model(&model.AcMember{})
	err = db.Count(&count).Error
	return
}

func GetMemberByUid(uid string) (*model.AcMember, error) {
	uid = strings.Trim(uid, " ")
	if UID, _ := strconv.ParseInt(uid, 10, 64); UID > 0 {
		uid = strconv.FormatInt(UID, 10)
		if mem, err := GetRedisMember(uid); err == nil {
			return mem, err
		}
	}
	var m model.AcMember
	err := global.DB.Model(&m).First(&m, "uid=?", uid).Error
	if err == nil {
		SetRedisMember(&m)
	}
	return &m, err
}

// CreateMember 新增账号
func CreateMember(m *model.AcMember) error {
	err := global.DB.Model(&model.AcMember{}).Create(m).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

// GetMemberByAccount 通过用户名获取账号
func GetMemberByAccount(account string) (member *model.AcMember, err error) {
	var m model.AcMember
	err = global.DB.Model(&model.AcMember{}).
		Where("username=? or email=? or mobile=? or unionid=?", account, account, account, account).First(&m).Error
	return &m, err
}

// ChangePassword 修改密码
func ChangePassword(m *model.AcMember, pwd string) error {
	FormatMemberPassword(m, pwd)
	m.AccessToken = utils.MD5(m.Password + m.Username)
	err := global.DB.Exec("Update `ac_members` SET `password`=?, `salt`=?, `access_token`=? WHERE `uid`=? Limit 1",
		m.Password, m.Salt, m.AccessToken, m.Uid).Error
	if err == nil {
		SetRedisMember(m)
		ResetSubmemberAccesstoken(m.Uid)
	}
	return err
}

// ResetPassword 重置密码
func ResetPassword(m *model.AcMember) error {
	err := global.DB.Exec("Update `ac_members` SET `salt`=?, `password`=?, `access_token`=?, `access_expire`=? WHERE `uid`=? Limit 1",
		m.Salt, m.Password, m.AccessToken, m.AccessExpire, m.Uid).Error
	if err == nil {
		SetRedisMember(m)
		ResetSubmemberAccesstoken(m.Uid)
	}
	return err
}

// CreateMemberAccessToken 生成access_token
func CreateMemberAccessToken(uid uint, timestamp int64) string {
	return utils.MD5(strconv.FormatInt(timestamp, 10) + strconv.Itoa(int(uid)))
}

// SaveMemberAccess 保存账号token和token过期时间
func SaveMemberAccess(m *model.AcMember) error {
	err := global.DB.Exec("Update `ac_members` SET `access_expire`=?, `access_token`=? WHERE `uid`=? Limit 1",
		m.AccessExpire, m.AccessToken, m.Uid).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

// GetMemberByUids 通过uids获取账号
func GetMemberByUids(uids string) (list []model.AcMember, err error) {
	ids := strings.Split(uids, ",")
	err = global.DB.Model(&model.AcMember{}).Find(&list, "uid in (?)", ids).Error
	return
}

// CheckPwd 检查密码与账号中记录的是否一致
func CheckPwd(m *model.AcMember, pwd string) bool {
	salt := m.Salt
	md5pwd := utils.MD5(pwd + salt)
	shapwd := utils.HmacSha256(pwd)[:32]
	shaSaltPwd := utils.HmacSha256(pwd + salt)[:32]
	if m.Password != md5pwd && m.Password != shapwd && m.Password != shaSaltPwd {
		return false
	}
	return true
}

// FormatMemberPassword 生成盐值并计算密码
func FormatMemberPassword(m *model.AcMember, password string) {
	m.Salt = strconv.Itoa(rand.Intn(900000) + 100000)
	m.Password = utils.HmacSha256(password + m.Salt)[:32]
}

// GetMemberByUsername 通过用户名获取账号
func GetMemberByUsername(username string) (*model.AcMember, error) {
	var m model.AcMember
	err := global.DB.Model(&m).First(&m, "username=?", username).Error
	return &m, err
}

// GetMemberByMobile 通过手机号获取账号
func GetMemberByMobile(mobile string) (*model.AcMember, error) {
	var m model.AcMember
	err := global.DB.Model(&m).First(&m, "mobile=?", mobile).Error
	return &m, err
}

// GetMemberByEmail 通过邮箱获取账号
func GetMemberByEmail(email string) (*model.AcMember, error) {
	var m model.AcMember
	err := global.DB.Model(&m).First(&m, "email=?", email).Error
	return &m, err
}

// GetWxMember 微信注册时用
func GetWxMember(unionid, mobile string) (m *model.AcMember, err error) {
	var mem model.AcMember
	err = global.DB.Model(&mem).First(&mem, "unionid=? or mobile=?", unionid, mobile).Error
	return &mem, err
}

// GetMemberByUnionid 通过微信unionid获取账号
func GetMemberByUnionid(unionid string) (m *model.AcMember, err error) {
	var mem model.AcMember
	err = global.DB.Model(&mem).First(&mem, "unionid=?", unionid).Error
	return &mem, err
}

// GetCtdidMember 网络身份认证注册时用，参考GetWxMember
func GetCtdidMember(ctdidPid, mobile string) (m *model.AcMember, err error) {
	var mem model.AcMember
	err = global.DB.Model(&mem).First(&mem, "ctdid_pid=? or mobile=?", ctdidPid, mobile).Error
	return &mem, err
}

// GetMemberByCtdid 通过网络身份认证ctdid_pid获取账号，参考GetMemberByUnionid
func GetMemberByCtdid(ctdidPid string) (m *model.AcMember, err error) {
	var mem model.AcMember
	err = global.DB.Model(&mem).First(&mem, "ctdid_pid=?", ctdidPid).Error
	return &mem, err
}



// SaveMember 保存账号信息
func SaveMember(m *model.AcMember) error {
	err := global.DB.Model(&model.AcMember{}).Where("uid=?", m.Uid).Limit(1).Save(m).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

// MemberIsExistsByUsernameAndEmail 通过用户名和邮箱查询账号是否存在
func MemberIsExistsByUsernameAndEmail(username, email string) bool {
	var m model.AcMember
	global.DB.Model(&model.AcMember{}).First(&m, "username=? or email=?", username, email)
	return m.Uid > 0
}

// MemberIsExistsByUsernameAndMobile 通过用户名和手机查询账号是否存在
func MemberIsExistsByUsernameAndMobile(username, mobile string) bool {
	var m model.AcMember
	global.DB.Model(&model.AcMember{}).First(&m, "username=? or mobile=?", username, mobile)
	return m.Uid > 0
}

// DeleteMemberByUid 通过uid删除账号
func DeleteMemberByUid(uid uint) error {
	err := global.DB.Exec("DELETE FROM `ac_members` WHERE `uid`=? Limit 1", uid).Error
	if err == nil {
		DelRedisMember(int(uid))
		ResetSubmemberAccesstoken(uid)
	}
	return err
}

// GetMemberByAccessToken 通过token获取账号
func GetMemberByAccessToken(access_token string) (*model.AcMember, error) {
	var m model.AcMember
	err := global.DB.Model(&model.AcMember{}).First(&m, "access_token=?", access_token).Error
	return &m, err
}

func GetMemberByMobiles(mobiles string) (list []model.AcMember, err error) {
	ms := strings.Split(mobiles, ",")
	err = global.DB.Model(&model.AcMember{}).Find(&list, "mobile in (?)", ms).Error
	return
}

func BindEmail(m *model.AcMember) error {
	err := global.DB.Exec("Update `ac_members` Set `username`=?, `email`=? Where `uid`=? Limit 1",
		m.Username, m.Email, m.Uid).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

func BindMobile(m *model.AcMember) error {
	err := global.DB.Exec("Update `ac_members` Set `username`=?, `mobile`=?, `country_code`=? Where `uid`=? Limit 1",
		m.Username, m.Mobile, m.CountryCode, m.Uid).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

func SetMemberForbidden(uid, forbidden int) error {
	err := global.DB.Exec("Update `ac_members` Set `forbidden`=? Where `uid`=? Limit 1", forbidden, uid).Error
	if err == nil {
		m, _ := GetRedisMember(strconv.Itoa(uid))
		if m != nil && m.Uid > 0 {
			m.Forbidden = forbidden
			SetRedisMember(m)
		}
	}
	return err
}

// CreateMemberCtx 新增账号
func CreateMemberCtx(c context.Context, m *model.AcMember) error {
	err := global.GetDB(c).Model(&model.AcMember{}).Create(m).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

// SaveMemberAccess 保存账号token和token过期时间
func SaveMemberAccessCtx(c context.Context, m *model.AcMember) error {
	err := global.GetDB(c).Exec("Update `ac_members` SET `access_expire`=?, `access_token`=? WHERE `uid`=? Limit 1",
		m.AccessExpire, m.AccessToken, m.Uid).Error
	if err == nil {
		SetRedisMember(m)
	}
	return err
}

// 判断是否10分钟内累计登录失败超过5次
func CheckLoginReachLimit(c context.Context, m *model.AcMember) (bool, error) {
	key := fmt.Sprintf("account:tryLogin:%d", m.Uid)
	tryTimes, err := global.Redis.Get(key).Int()
	if err != nil && !errors.Is(err, redis.Nil) {
		return false, err
	}
	if tryTimes >= 5 {
		return true, nil
	}
	return false, nil
}

func SetTryLogin(c context.Context, m *model.AcMember, loginOk bool) (err error) {
	key := fmt.Sprintf("account:tryLogin:%d", m.Uid)
	if loginOk {
		_, err = global.Redis.Del(key).Result()
	} else {
		pipe := global.Redis.Pipeline()
		pipe.Incr(key)
		pipe.Expire(key, time.Minute*10)
		pipe.Exec()
	}
	return
}

func GetMemberByMobileAppid(c context.Context, mobile, appid string) (*model.AcMember, error) {
	var m model.AcMember
	db := global.DB.Model(&m).Where("mobile = ?", mobile)
	if appid != "" {
		global.DB.Table("empower_members").Where("mobile = ? And appid = ?", mobile, appid).Select("uid").Scan(&m)
		if m.Uid > 0 {
			err := global.DB.Model(&m).First(&m, m.Uid).Error
			if err != nil {
				return nil, err
			}
			return &m, nil
		}
		db = db.Where("regapp = ?", appid)
	}
	err := db.First(&m).Error
	return &m, err
}

func MemberUnregister(uid uint) (err error) {
	err = global.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Exec("DELETE FROM `user_profile` WHERE `uid`=? Limit 1", uid).Error; err != nil {
			return err
		}
		if err := tx.Model(model.UserFace{}).Where("uid = ?", uid).Delete(&map[string]interface{}{}).Error; err != nil {
			return err
		}
		if err := tx.Exec("DELETE FROM `ac_members` WHERE `uid`=? Limit 1", uid).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		err = response.New(response.E_DATABASE, "", err)
		return
	}
	DelRedisMember(int(uid))
	ResetSubmemberAccesstoken(uid)
	return
}

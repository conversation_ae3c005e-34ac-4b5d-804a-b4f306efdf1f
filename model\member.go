package model

type AcMember struct {
	Uid          uint `gorm:"primarykey;autoIncrement"`
	Username     string
	Password     string
	Salt         string
	Email        string
	Unionid      string
	CtdidPid     string `gorm:"-"` // 忽略此字段，不再读写数据库
	Mobile       string
	CountryCode  string
	Regip        string
	Regdate      int64
	Regapp       string
	Secques      string
	Forbidden    int `gorm:"default:0;comment:'0 正常; 1 禁用; 2 特殊; 4 未激活'"` // 暂时只有邮件注册需要激活
	AccessToken  string
	AccessExpire int64
}

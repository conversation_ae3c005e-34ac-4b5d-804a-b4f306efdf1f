package v1

import (
	"account/global"
	"account/model"
	"account/model/response"
	"account/service"
	"account/utils"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// CtdidRegister 绑定网络身份认证或用网络身份认证注册
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param ctdid_pid string true "网络身份认证PID"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Param password string false "密码，注册时必填"
// @Param sex int false "性别，1 男；2 女"
// @Param nickname string false "昵称"
// @Param province string false "地区（省）"
// @Param city string false "地区（市）"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********}
// @Router /ctdid/register [get]
func CtdidRegister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithErrcode(response.E_INVALID_SN, desp, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	ctdidPid, ok := GetAndCheckCtdidPid(c)
	if !ok {
		return
	}
	countryCode, ok := CheckSMSVerify(c, mobile)
	if !ok {
		return
	}
	password := GetRequestParam(c, "password")
	if password != "" && !utils.TestPassword(password) {
		password = utils.MD5(password)
	}
	now := time.Now().Unix()
	isRegister := false // 是否为注册
	m, err := service.GetCtdidMember(ctdidPid, mobile)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		if password == "" {
			response.FailWithErrcode(response.E_REQUIRE_PWD, "", c)
			return
		}
		m.Regip = GetIp(c)
		m.Regdate = now
		m.Regapp = GetAppid(c)
		service.FormatMemberPassword(m, password)
		m.AccessToken = utils.MD5(mobile + strconv.FormatInt(now, 10))
		isRegister = true
	}
	m.Username = mobile
	m.CtdidPid = ctdidPid
	m.AccessExpire = now + global.TOKEN_EXPIRE
	m.Forbidden = 0
	m.Mobile = mobile
	m.CountryCode = countryCode

	if isRegister {
		if err = service.CreateMember(m); err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	} else {
		if err = service.SaveMember(m); err != nil {
			response.FailWithErrcode(response.E_DATABASE, "", c)
			return
		}
	}

	// 创建或更新用户状态
	service.CreateOrUpdateUserStatus(m.Uid, GetIp(c), GetAppid(c), now)

	// 创建或更新用户资料
	if p, err := service.GetProfileByUid(m.Uid); err == nil {
		if sex := GetRequestParam(c, "sex"); sex == "2" {
			p.Gender = 1
		} else if sex == "1" {
			p.Gender = 0
		}
		province := GetRequestParam(c, "province")
		city := GetRequestParam(c, "city")
		if province != "" {
			p.Address = province + "-" + city
		}
		if realname := GetRequestParam(c, "nickname"); realname != "" && p.Realname == "" {
			p.Realname = realname
		}
		service.SaveProfile(p)
	} else {
		p := model.UserProfile{}
		p.Uid = int(m.Uid)
		p.Username = m.Username
		if sex := GetRequestParam(c, "sex"); sex == "2" {
			p.Gender = 1
		} else if sex == "1" {
			p.Gender = 0
		}
		province := GetRequestParam(c, "province")
		city := GetRequestParam(c, "city")
		if province != "" {
			p.Address = province + "-" + city
		}
		if realname := GetRequestParam(c, "nickname"); realname != "" {
			p.Realname = realname
		}
		service.CreateProfile(&p)
	}
	data := getMemberLoginInfo(m)
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	response.OkWithData(data, c)
}

// CtdidLogin 网络身份认证登录
// login no
// @Param sn string true "签名"
// @Param ctdid_pid string true "网络身份认证PID"
// @Success 200 {json} json {"uid": 1, "username": "", "mobile": "", "forbidden": 0, "access_token": "", "access_expire": **********, "avatar": ""}
// @Router /ctdid/login [get]
func CtdidLogin(c *gin.Context) {
	reason, desp, m := CheckSignature(c)
	if reason < 0 && reason != -2 {
		response.FailWithErrcode(response.E_INVALID_SN, desp, c)
		return
	}
	now := time.Now().Unix()
	if reason == 1 {
		m.AccessExpire = now + global.TOKEN_EXPIRE
		service.SaveMemberAccess(m)
		data := getMemberLoginInfo(m)
		data["access_expire"] = m.AccessExpire
		data["email"] = m.Email
		data["mobile"] = m.Mobile
		data["country_code"] = m.CountryCode
		data["avatar"] = utils.GetAvatarUri(m.Uid)
		response.OkWithData(data, c)
		return
	}
	ctdidPid, ok := GetAndCheckCtdidPid(c)
	if !ok {
		return
	}
	m, err := service.GetMemberByCtdid(ctdidPid)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if !CheckMemberForbidden(c, m) {
		return
	}
	if now >= m.AccessExpire {
		m.AccessToken = service.CreateMemberAccessToken(m.Uid, now)
	}
	m.AccessExpire = now + global.TOKEN_EXPIRE
	service.SaveMemberAccess(m)
	appid := GetAppid(c)
	service.CreateOrUpdateUserStatus(m.Uid, GetIp(c), appid, now)
	data := getMemberLoginInfo(m)
	data["access_expire"] = m.AccessExpire
	data["email"] = m.Email
	data["mobile"] = m.Mobile
	data["country_code"] = m.CountryCode
	data["avatar"] = utils.GetAvatarUri(m.Uid)
	response.OkWithData(data, c)
}

// CtdidUnregister 网络身份认证解绑
// login no
// @Param sn string true "签名"
// @Param mobile string true "手机号"
// @Param ctdid_pid string true "网络身份认证PID"
// @Param serial string true "验证码序列号"
// @param verify string true "验证码"
// @Success {json} json {"status": 0}
// @Router /ctdid/unregister [get]
func CtdidUnregister(c *gin.Context) {
	reason, desp, _ := CheckSignature(c)
	if reason < 0 {
		response.FailWithDetail(response.E_INVALID_SN, desp, gin.H{"reason": reason, "desp": desp}, c)
		return
	}
	mobile, ok := GetAndCheckMobile(c)
	if !ok {
		return
	}
	ctdidPid, ok := GetAndCheckCtdidPid(c)
	if !ok {
		return
	}
	if !CheckVerify(c, mobile) {
		return
	}
	m, err := service.GetMemberByMobile(mobile)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.FailWithErrcode(response.E_MEMBER_NOT_EXISTS, "", c)
		} else {
			response.FailWithErrcode(response.E_DATABASE, "", c)
		}
		return
	}
	if m.CtdidPid != ctdidPid {
		response.FailWithErrcode(response.E_ACCESS, "用户未绑定该网络身份认证", c)
		return
	}
	m.CtdidPid = ""
	if err := service.SaveMember(m); err == nil {
		response.OkWithData(gin.H{"status": 0}, c)
		return
	}
	response.FailWithErrcode(response.E_DATABASE, "", c)
}

# CTDID 网络身份认证接口文档

## 概述

CTDID（网络身份认证）接口提供用户注册、登录和解绑功能，支持通过网络身份认证PID进行用户身份验证。

**基础URL**: `https://<host>/api/v1/ctdid`

## 接口列表

### 1. 注册/绑定接口

**接口编号**: 01  
**接口描述**: 绑定网络身份认证或用网络身份认证注册新用户

#### 请求信息
- **URL**: `/register`
- **方法**: `GET` / `POST`
- **认证**: 需要签名验证

#### 请求参数
| 参数名 | 必传 | 类型 | 描述 |
|--------|------|------|------|
| sn | Y | string | 签名 |
| mobile | Y | string | 手机号（11位） |
| ctdid_pid | Y | string | 网络身份认证PID（40位字母数字） |
| serial | Y | string | 验证码序列号 |
| verify | Y | string | 验证码 |
| password | N | string | 密码（注册时必填，MD5格式） |
| sex | N | int | 性别（1=男，2=女） |
| nickname | N | string | 昵称 |
| province | N | string | 地区（省） |
| city | N | string | 地区（市） |

#### 返回值
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "uid": 12345,
    "username": "13800138000",
    "mobile": "13800138000",
    "forbidden": 0,
    "access_token": "abc123...",
    "access_expire": 1234567890,
    "country_code": "+86"
  }
}
```

#### 业务逻辑
1. 验证签名和参数格式
2. 验证短信验证码
3. 检查用户是否已存在（通过ctdid_pid或mobile）
4. 如果用户不存在且提供了密码，创建新用户
5. 如果用户存在，更新绑定信息
6. 处理用户资料信息（性别、昵称、地区等）
7. 返回用户登录信息

---

### 2. 登录接口

**接口编号**: 02  
**接口描述**: 通过网络身份认证PID登录

#### 请求信息
- **URL**: `/login`
- **方法**: `GET` / `POST`
- **认证**: 需要签名验证

#### 请求参数
| 参数名 | 必传 | 类型 | 描述 |
|--------|------|------|------|
| sn | Y | string | 签名 |
| ctdid_pid | Y | string | 网络身份认证PID（40位字母数字） |

#### 返回值
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "uid": 12345,
    "username": "13800138000",
    "mobile": "13800138000",
    "forbidden": 0,
    "access_token": "abc123...",
    "access_expire": 1234567890,
    "email": "<EMAIL>",
    "country_code": "+86",
    "avatar": "http://img.readboy.com/avatar/12345.jpg"
  }
}
```

#### 业务逻辑
1. 验证签名和参数格式
2. 检查是否已登录（通过签名中的token）
3. 如果已登录，刷新token并返回用户信息
4. 如果未登录，通过ctdid_pid查找用户
5. 检查用户状态（是否被禁用）
6. 生成或刷新access_token
7. 更新用户状态和最后登录时间
8. 返回完整的用户登录信息

---

### 3. 解绑接口

**接口编号**: 03  
**接口描述**: 解除网络身份认证绑定

#### 请求信息
- **URL**: `/unregister`
- **方法**: `GET` / `POST`
- **认证**: 需要签名验证

#### 请求参数
| 参数名 | 必传 | 类型 | 描述 |
|--------|------|------|------|
| sn | Y | string | 签名 |
| mobile | Y | string | 手机号（11位） |
| ctdid_pid | Y | string | 网络身份认证PID（40位字母数字） |
| serial | Y | string | 验证码序列号 |
| verify | Y | string | 验证码 |

#### 返回值
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "status": 0
  }
}
```

#### 业务逻辑
1. 验证签名和参数格式
2. 验证短信验证码
3. 通过手机号查找用户
4. 验证用户是否绑定了该ctdid_pid
5. 清空用户的ctdid_pid字段
6. 保存用户信息
7. 返回成功状态

---

## 通用说明

### 签名机制
所有接口都需要签名验证，签名格式：
```
sn = uid + timestamp + md5(timestamp + appsec + token) + appid
```

- `uid`: 用户ID，未登录时为 "00000000"
- `timestamp`: 10位时间戳
- `appsec`: 应用密钥
- `token`: 未登录时为 md5(appid)，已登录时为用户的access_token
- `appid`: 应用ID

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 签名格式错误 |
| 1002 | 签名无效 |
| 1003 | 参数错误 |
| 1004 | 手机号格式错误 |
| 1005 | ctdid_pid格式错误 |
| 1006 | 验证码错误 |
| 1007 | 用户不存在 |
| 1008 | 用户被禁用 |
| 1009 | 数据库错误 |

### CTDID_PID格式要求
- 长度：40个字符
- 字符集：字母（a-z, A-Z）和数字（0-9）
- 示例：`1234567890abcdef1234567890abcdef12345678`

### 验证码获取
在调用注册和解绑接口前，需要先调用获取验证码接口：
```
GET /api/v1/mobile/verify?sn=<签名>&mobile=<手机号>&type=1
```

### 注意事项
1. 所有时间戳均为Unix时间戳（秒）
2. 密码需要MD5加密后传输
3. 签名有效期为5分钟
4. access_token有效期为30天
5. 验证码有效期为5分钟
6. 同一手机号每分钟最多获取一次验证码

### 示例代码

#### JavaScript示例
```javascript
// 生成签名
function generateSignature(uid, appid, appsec, token) {
    const timestamp = Math.floor(Date.now() / 1000);
    const timestr = timestamp.toString();
    const md5str = md5(timestr + appsec + token);
    return uid + timestr + md5str + appid;
}

// 注册请求
async function ctdidRegister(mobile, ctdidPid, password, serial, verify) {
    const sn = generateSignature('00000000', 'your_appid', 'your_appsec', md5('your_appid'));
    
    const response = await fetch('/api/v1/ctdid/register', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            sn: sn,
            mobile: mobile,
            ctdid_pid: ctdidPid,
            password: password,
            serial: serial,
            verify: verify
        })
    });
    
    return await response.json();
}
```

#### Go示例
```go
// 生成签名
func generateSignature(uid, appid, appsec, token string) string {
    timestamp := time.Now().Unix()
    timestr := strconv.FormatInt(timestamp, 10)
    md5str := utils.MD5(timestr + appsec + token)
    return uid + timestr + md5str + appid
}

// 登录请求
func ctdidLogin(ctdidPid string) (*LoginResponse, error) {
    sn := generateSignature("00000000", "your_appid", "your_appsec", utils.MD5("your_appid"))
    
    params := url.Values{}
    params.Set("sn", sn)
    params.Set("ctdid_pid", ctdidPid)
    
    resp, err := http.Get("/api/v1/ctdid/login?" + params.Encode())
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var result LoginResponse
    err = json.NewDecoder(resp.Body).Decode(&result)
    return &result, err
}
```

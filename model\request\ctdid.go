package request

// CtdidRegisterRequest 网络身份认证注册请求
type CtdidRegisterRequest struct {
	Sn       string `form:"sn" json:"sn" binding:"required"`             // 签名
	Mobile   string `form:"mobile" json:"mobile" binding:"required"`     // 手机号
	CtdidPid string `form:"ctdid_pid" json:"ctdid_pid" binding:"required"` // 网络身份认证PID
	Serial   string `form:"serial" json:"serial" binding:"required"`     // 验证码序列号
	Verify   string `form:"verify" json:"verify" binding:"required"`     // 验证码
	Password string `form:"password" json:"password"`                    // 密码，注册时必填
}

// CtdidLoginRequest 网络身份认证登录请求
type CtdidLoginRequest struct {
	Sn       string `form:"sn" json:"sn" binding:"required"`             // 签名
	CtdidPid string `form:"ctdid_pid" json:"ctdid_pid" binding:"required"` // 网络身份认证PID
}

// CtdidUnregisterRequest 网络身份认证解绑请求
type CtdidUnregisterRequest struct {
	Sn       string `form:"sn" json:"sn" binding:"required"`             // 签名
	Mobile   string `form:"mobile" json:"mobile" binding:"required"`     // 手机号
	CtdidPid string `form:"ctdid_pid" json:"ctdid_pid" binding:"required"` // 网络身份认证PID
	Serial   string `form:"serial" json:"serial" binding:"required"`     // 验证码序列号
	Verify   string `form:"verify" json:"verify" binding:"required"`     // 验证码
}
